#!/bin/bash

# Time Tracking App - AWS 部署状态检查脚本
# 此脚本检查 AWS CloudFormation 堆栈的部署状态

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # 无颜色

# 输出函数
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 配置
STACK_NAME="time-tracking-app"
# 从AWS配置文件中读取区域，如果未设置则使用us-east-1作为默认值
DEFAULT_REGION=$(aws configure get region 2>/dev/null || echo "us-east-1")
REGION="${AWS_REGION:-$DEFAULT_REGION}"

print_status "检查 AWS CloudFormation 堆栈状态..."
print_status "  Stack 名称: $STACK_NAME"
print_status "  区域: $REGION"

# 检查 AWS CLI 是否已安装
if ! command -v aws &> /dev/null; then
    print_error "未安装 AWS CLI。请安装 AWS CLI 后重试。"
    print_status "安装指南: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html"
    exit 1
fi

# 检查 AWS 凭证
if ! aws sts get-caller-identity &> /dev/null; then
    print_error "未配置 AWS 凭证。请先运行 'aws configure'。"
    exit 1
fi

# 获取堆栈状态
print_step "获取堆栈状态..."
STACK_STATUS=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --region $REGION --query 'Stacks[0].StackStatus' --output text 2>/dev/null)

if [ $? -ne 0 ]; then
    print_error "堆栈 '$STACK_NAME' 不存在或无法访问。"
    print_status "请先运行部署命令: npm run deploy:aws:enhanced"
    exit 1
fi

# 显示堆栈状态
print_status "堆栈状态: $STACK_STATUS"

# 根据状态显示不同信息
case $STACK_STATUS in
    "CREATE_COMPLETE"|"UPDATE_COMPLETE")
        print_success "✅ 部署成功！"
        
        # 获取输出
        print_step "获取部署输出..."
        echo ""
        aws cloudformation describe-stacks --stack-name $STACK_NAME --region $REGION --query 'Stacks[0].Outputs' --output table
        
        # 获取应用 URL
        APP_URL=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --region $REGION --query 'Stacks[0].Outputs[?OutputKey==`WebsiteURL`].OutputValue' --output text)
        if [ -n "$APP_URL" ]; then
            print_success "应用访问地址: $APP_URL"
        fi
        ;;
    "CREATE_IN_PROGRESS"|"UPDATE_IN_PROGRESS")
        print_warning "⏳ 部署正在进行中..."
        print_status "可以使用以下命令查看实时状态:"
        print_status "aws cloudformation describe-stack-events --stack-name $STACK_NAME --region $REGION"
        ;;
    "CREATE_FAILED"|"ROLLBACK_IN_PROGRESS"|"ROLLBACK_COMPLETE"|"UPDATE_ROLLBACK_IN_PROGRESS"|"UPDATE_ROLLBACK_COMPLETE")
        print_error "❌ 部署失败或回滚！"
        print_status "查看失败原因:"
        aws cloudformation describe-stack-events --stack-name $STACK_NAME --region $REGION --query 'StackEvents[?ResourceStatus==`CREATE_FAILED` || ResourceStatus==`UPDATE_FAILED`].[LogicalResourceId,ResourceStatusReason]' --output table
        ;;
    *)
        print_warning "当前堆栈状态: $STACK_STATUS"
        print_status "使用以下命令查看详细信息:"
        print_status "aws cloudformation describe-stacks --stack-name $STACK_NAME --region $REGION"
        ;;
esac

# 显示资源列表
print_step "获取已部署资源列表..."
echo ""
aws cloudformation list-stack-resources --stack-name $STACK_NAME --region $REGION --query 'StackResourceSummaries[].{LogicalID:LogicalResourceId,Type:ResourceType,Status:ResourceStatus}' --output table