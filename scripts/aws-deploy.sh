#!/bin/bash

# Time Tracking App - AWS 部署脚本
# 此脚本整合了所有AWS部署功能，提供简单和增强两种部署模式

set -e  # 遇到错误时退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # 无颜色

# 输出函数
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 获取项目目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_DIR"

print_status "工作目录: $PROJECT_DIR"

# 配置
STACK_NAME="time-tracking-app"
# 从AWS配置文件中读取区域，如果未设置则使用us-east-1作为默认值
DEFAULT_REGION=$(aws configure get region 2>/dev/null || echo "us-east-1")
REGION="${AWS_REGION:-$DEFAULT_REGION}"
MODE="${1:-simple}" # 默认为简单模式，可选参数: simple, enhanced

# 显示欢迎信息
echo "
╔════════════════════════════════════════════════════════════╗
║                                                            ║
║   Time Tracking App - AWS 部署工具                         ║
║                                                            ║
║   模式: ${MODE}                                             ║
║   区域: ${REGION}                                           ║
║                                                            ║
╚════════════════════════════════════════════════════════════╝
"

# 检查先决条件
check_prerequisites() {
    print_step "检查部署先决条件..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        print_error "未安装 Node.js。请安装 Node.js 后重试。"
        print_status "安装指南: https://nodejs.org/en/download/"
        exit 1
    fi
    
    NODE_VERSION=$(node -v)
    print_status "检测到 Node.js 版本: $NODE_VERSION"
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        print_error "未安装 npm。请安装 npm 后重试。"
        exit 1
    fi
    
    NPM_VERSION=$(npm -v)
    print_status "检测到 npm 版本: $NPM_VERSION"
    
    # 检查 AWS CLI
    if ! command -v aws &> /dev/null; then
        print_error "未安装 AWS CLI。请安装 AWS CLI 后重试。"
        print_status "安装指南: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html"
        exit 1
    fi
    
    AWS_CLI_VERSION=$(aws --version 2>&1 | cut -d' ' -f1)
    print_status "检测到 AWS CLI 版本: $AWS_CLI_VERSION"
    
    # 检查 AWS 凭证
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "未配置 AWS 凭证。请运行 'aws configure' 配置凭证。"
        print_status "配置指南: https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-quickstart.html"
        exit 1
    fi
    
    AWS_ACCOUNT=$(aws sts get-caller-identity --query Account --output text)
    AWS_USER=$(aws sts get-caller-identity --query Arn --output text)
    print_success "已为账户 $AWS_ACCOUNT 配置 AWS 凭证"
    print_status "当前用户: $AWS_USER"
    print_status "部署区域: $REGION"
}

# 安装依赖
install_dependencies() {
    print_step "安装项目依赖..."
    
    # 检查 node_modules 目录
    if [ ! -d "node_modules" ] || [ "$MODE" == "enhanced" ]; then
        print_status "安装 npm 依赖..."
        npm install
    else
        print_status "node_modules 目录已存在，跳过安装"
    fi
    
    # 检查 AWS CDK
    if ! command -v npx cdk &> /dev/null; then
        print_status "安装 AWS CDK..."
        npm install -g aws-cdk
    fi
    
    print_success "依赖安装完成"
}

# 构建应用
build_app() {
    print_step "构建应用..."
    
    # 清理之前的构建
    if [ -d ".next" ] || [ -d "out" ]; then
        print_status "清理之前的构建..."
        
        # 尝试常规删除
        rm -rf .next out 2>/dev/null || true
        
        # 如果常规删除失败，使用修复脚本
        if [ -d ".next" ] || [ -d "out" ]; then
            print_warning "常规清理失败，尝试使用修复脚本..."
            chmod +x "$SCRIPT_DIR/fix-next-dir.sh"
            "$SCRIPT_DIR/fix-next-dir.sh"
        fi
    fi
    
    # 构建应用
    print_status "执行构建..."
    npm run build
    
    print_success "应用构建完成"
}

# 部署到 AWS
deploy_to_aws() {
    print_step "部署到 AWS..."
    
    # 检查 infrastructure 目录
    if [ ! -d "infrastructure" ]; then
        print_error "未找到 infrastructure 目录。请确保项目结构正确。"
        exit 1
    fi
    
    # 进入 infrastructure 目录
    cd infrastructure
    
    # 安装 CDK 依赖
    if [ ! -d "node_modules" ] || [ "$MODE" == "enhanced" ]; then
        print_status "安装 CDK 依赖..."
        npm install
    fi
    
    # 部署 CDK 堆栈
    print_status "部署 CDK 堆栈..."
    npx cdk deploy --require-approval never
    
    # 返回项目根目录
    cd "$PROJECT_DIR"
    
    print_success "AWS 部署完成"
}

# 清除 CloudFront 缓存（如果存在）
invalidate_cache() {
    print_step "检查是否需要清除 CloudFront 缓存..."
    
    # 尝试获取 CloudFront 分发 ID
    DISTRIBUTION_ID=$(aws cloudformation describe-stacks \
        --stack-name $STACK_NAME \
        --region $REGION \
        --query 'Stacks[0].Outputs[?OutputKey==`CloudFrontDistributionId`].OutputValue' \
        --output text 2>/dev/null)
    
    if [ -n "$DISTRIBUTION_ID" ] && [ "$DISTRIBUTION_ID" != "None" ]; then
        print_status "找到 CloudFront 分发 ID: $DISTRIBUTION_ID"
        print_status "创建缓存失效请求..."
        
        INVALIDATION_ID=$(aws cloudfront create-invalidation \
            --distribution-id $DISTRIBUTION_ID \
            --paths "/*" \
            --query 'Invalidation.Id' \
            --output text)
        
        print_success "已创建缓存失效请求 (ID: $INVALIDATION_ID)"
        print_status "缓存失效可能需要 5-15 分钟完成"
    else
        print_status "未找到 CloudFront 分发，跳过缓存失效"
    fi
}

# 显示部署输出
show_outputs() {
    print_step "获取部署输出..."
    
    # 获取堆栈输出
    OUTPUTS=$(aws cloudformation describe-stacks \
        --stack-name $STACK_NAME \
        --region $REGION \
        --query 'Stacks[0].Outputs' \
        --output json 2>/dev/null)
    
    if [ $? -eq 0 ] && [ "$OUTPUTS" != "null" ]; then
        echo ""
        echo "部署输出:"
        echo "$OUTPUTS" | jq -r '.[] | "  \(.OutputKey): \(.OutputValue)"'
        echo ""
        
        # 获取应用 URL
        WEBSITE_URL=$(echo "$OUTPUTS" | jq -r '.[] | select(.OutputKey=="WebsiteURL") | .OutputValue')
        if [ -n "$WEBSITE_URL" ] && [ "$WEBSITE_URL" != "null" ]; then
            print_success "应用访问地址: $WEBSITE_URL"
        fi
    else
        print_warning "无法获取部署输出"
    fi
}

# 主函数
main() {
    # 检查先决条件
    check_prerequisites
    
    # 根据模式执行不同的部署流程
    if [ "$MODE" == "enhanced" ]; then
        print_status "使用增强模式部署..."
        install_dependencies
        build_app
        deploy_to_aws
        invalidate_cache
        show_outputs
    else
        print_status "使用简单模式部署..."
        build_app
        deploy_to_aws
        show_outputs
    fi
    
    print_success "部署流程完成"
    print_status "可以使用以下命令检查部署状态:"
    print_status "  ./scripts/aws-status.sh"
}

# 执行主函数
main