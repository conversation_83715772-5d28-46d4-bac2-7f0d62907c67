#!/bin/bash

# Time Tracking App - AWS 部署脚本 (增强版)
# 此脚本使用 AWS CDK 部署应用程序到 AWS
# 增强了错误处理、缓存失效和部署状态跟踪

set -e  # 遇到错误时退出

echo "☁️ 开始部署 Time Tracking App 到 AWS..."

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # 无颜色

# 输出函数
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 配置
STACK_NAME="time-tracking-app"
# 从AWS配置文件中读取区域，如果未设置则使用us-east-1作为默认值
DEFAULT_REGION=$(aws configure get region 2>/dev/null || echo "us-east-1")
REGION="${AWS_REGION:-$DEFAULT_REGION}"
ENVIRONMENT="${ENVIRONMENT:-production}"
DEPLOYMENT_ID=$(date +%Y%m%d%H%M%S)
LOG_FILE="/tmp/aws-deploy-${DEPLOYMENT_ID}.log"

print_status "部署配置:"
print_status "  Stack 名称: $STACK_NAME"
print_status "  区域: $REGION"
print_status "  环境: $ENVIRONMENT"
print_status "  部署 ID: $DEPLOYMENT_ID"
print_status "  日志文件: $LOG_FILE"

# 检查 AWS CLI 是否已安装
if ! command -v aws &> /dev/null; then
    print_error "未安装 AWS CLI。请安装 AWS CLI 后重试。"
    print_status "安装指南: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html"
    exit 1
fi

# 检查 jq 是否已安装（用于解析JSON）
if ! command -v jq &> /dev/null; then
    print_warning "未安装 jq 工具（用于解析JSON）。正在尝试安装..."
    
    # 检测操作系统并安装jq
    if [ -f /etc/debian_version ]; then
        # Debian/Ubuntu
        sudo apt-get update && sudo apt-get install -y jq
    elif [ -f /etc/redhat-release ]; then
        # CentOS/RHEL
        sudo yum install -y jq
    elif [ -f /etc/arch-release ]; then
        # Arch Linux
        sudo pacman -S --noconfirm jq
    elif command -v brew &> /dev/null; then
        # macOS with Homebrew
        brew install jq
    else
        print_warning "无法自动安装jq。将使用备用方法检查package-lock.json。"
    fi
fi

# 检查 AWS CDK 是否已安装
if ! command -v cdk &> /dev/null; then
    print_warning "未安装 AWS CDK。正在全局安装..."
    npm install -g aws-cdk
    
    # 再次检查是否安装成功
    if ! command -v cdk &> /dev/null; then
        print_error "AWS CDK 安装失败。请手动安装后重试。"
        print_status "安装命令: npm install -g aws-cdk"
        exit 1
    fi
fi

print_status "检测到 AWS CLI $(aws --version)"
print_status "检测到 AWS CDK $(cdk --version)"

# 检查 AWS 凭证
if ! aws sts get-caller-identity &> /dev/null; then
    print_error "未配置 AWS 凭证。请先运行 'aws configure'。"
    exit 1
fi

AWS_ACCOUNT=$(aws sts get-caller-identity --query Account --output text)
print_success "已为账户 $AWS_ACCOUNT 配置 AWS 凭证"

# 导航到项目目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_DIR"

# 安装依赖 (增强错误处理)
print_step "安装依赖..."

# 检查是否存在问题目录
if [ -d "node_modules/@next/swc-linux-x64-gnu" ]; then
    print_warning "检测到可能有问题的依赖目录，尝试修复..."
    
    # 尝试运行修复脚本
    if [ -f "./scripts/fix-npm-deps.sh" ]; then
        print_status "运行依赖修复脚本..."
        chmod +x ./scripts/fix-npm-deps.sh
        if ! ./scripts/fix-npm-deps.sh; then
            print_warning "修复脚本未能完全解决问题，尝试强制清理..."
            
            # 尝试运行强制清理脚本
            if [ -f "./scripts/force-clean.sh" ]; then
                print_status "运行强制清理脚本..."
                chmod +x ./scripts/force-clean.sh
                if ! ./scripts/force-clean.sh; then
                    print_error "强制清理失败。请手动删除 node_modules 目录后重试。"
                    print_status "手动命令: sudo rm -rf node_modules && npm cache clean --force && npm install"
                    exit 1
                fi
            else
                print_error "未找到强制清理脚本。请手动删除 node_modules 目录后重试。"
                print_status "手动命令: sudo rm -rf node_modules && npm cache clean --force && npm install"
                exit 1
            fi
        fi
    else
        print_warning "未找到依赖修复脚本，尝试手动修复..."
        
        # 手动修复
        print_status "修复文件权限..."
        find node_modules -type d -name "swc-linux-x64-gnu" -exec chmod -R 777 {} \; 2>/dev/null || true
        
        print_status "删除问题目录..."
        rm -rf node_modules/@next/swc-linux-x64-gnu 2>/dev/null || true
        
        print_status "清理 npm 缓存..."
        npm cache clean --force
        
        print_status "删除 node_modules 目录..."
        rm -rf node_modules
    fi
fi

# 尝试安装依赖
print_status "安装依赖..."
# 检查是否存在package-lock.json文件
VALID_LOCKFILE=false

if [ -f "package-lock.json" ]; then
    if command -v jq &> /dev/null; then
        # 使用jq检查lockfileVersion
        LOCKFILE_VERSION=$(jq -r '.lockfileVersion' package-lock.json 2>/dev/null)
        if [ "$LOCKFILE_VERSION" -ge "1" ] 2>/dev/null; then
            VALID_LOCKFILE=true
        fi
    else
        # 备用方法：使用grep检查lockfileVersion
        if grep -q '"lockfileVersion": [1-9]' package-lock.json 2>/dev/null; then
            VALID_LOCKFILE=true
        fi
    fi
fi

if [ "$VALID_LOCKFILE" = true ]; then
    print_status "检测到有效的package-lock.json，使用npm ci安装..."
    if ! npm ci; then
        print_warning "使用 npm ci 安装依赖失败，尝试使用 npm install..."
        if ! npm install --no-fund --no-audit; then
            print_error "依赖安装失败。请尝试以下步骤："
            print_status "1. 运行修复脚本: ./scripts/fix-npm-deps.sh"
            print_status "2. 如果仍然失败，运行强制清理脚本: sudo ./scripts/force-clean.sh"
            print_status "3. 然后重新运行此部署脚本"
            exit 1
        fi
    fi
else
    print_status "未检测到有效的package-lock.json，使用npm install安装..."
    if ! npm install --no-fund --no-audit; then
        print_error "依赖安装失败。请尝试以下步骤："
        print_status "1. 运行修复脚本: ./scripts/fix-npm-deps.sh"
        print_status "2. 如果仍然失败，运行强制清理脚本: sudo ./scripts/force-clean.sh"
        print_status "3. 然后重新运行此部署脚本"
        exit 1
    fi
fi

print_success "依赖安装完成"

# 构建应用
print_step "构建应用..."
if ! npm run build; then
    print_error "应用构建失败。"
    exit 1
fi

# 检查构建输出目录是否存在
if [ ! -d "out" ]; then
    print_error "构建输出目录 'out' 不存在。请检查 next.config.js 中的 output 和 distDir 配置。"
    exit 1
fi

print_success "应用构建完成"

# 创建 CDK 基础设施目录（如果不存在）
if [ ! -d "infrastructure" ]; then
    print_step "创建 CDK 基础设施..."
    mkdir infrastructure
    cd infrastructure
    
    # 初始化 CDK 项目
    cdk init app --language typescript
    
    # 安装额外依赖
    npm install @aws-cdk/aws-s3 @aws-cdk/aws-cloudfront @aws-cdk/aws-s3-deployment
    
    cd ..
fi

# 使用 CDK 部署
print_step "部署到 AWS..."
cd infrastructure

# 如果需要，引导 CDK
if ! aws cloudformation describe-stacks --stack-name CDKToolkit --region $REGION &> /dev/null; then
    print_status "引导 CDK..."
    cdk bootstrap aws://$AWS_ACCOUNT/$REGION
fi

# 部署堆栈
print_step "部署 CloudFormation 堆栈..."
if cdk deploy $STACK_NAME --require-approval never | tee -a $LOG_FILE; then
    print_success "🎉 AWS 部署成功完成！"
    
    # 获取 CloudFront URL 和分发 ID
    print_step "获取部署信息..."
    CLOUDFRONT_URL=$(aws cloudformation describe-stacks \
        --stack-name $STACK_NAME \
        --region $REGION \
        --query 'Stacks[0].Outputs[?OutputKey==`CloudFrontURL`].OutputValue' \
        --output text)
    
    DISTRIBUTION_ID=$(aws cloudformation describe-stacks \
        --stack-name $STACK_NAME \
        --region $REGION \
        --query 'Stacks[0].Outputs[?OutputKey==`CloudFrontDistributionId`].OutputValue' \
        --output text)
    
    if [ ! -z "$CLOUDFRONT_URL" ]; then
        print_success "应用 URL: $CLOUDFRONT_URL"
        
        # 创建 CloudFront 缓存失效以清除缓存
        if [ ! -z "$DISTRIBUTION_ID" ]; then
            print_step "创建 CloudFront 缓存失效..."
            
            # 检查 CloudFront 分发是否存在
            DISTRIBUTION_EXISTS=$(aws cloudfront get-distribution --id $DISTRIBUTION_ID 2>/dev/null | jq -r '.Distribution.Id')
            
            if [ -z "$DISTRIBUTION_EXISTS" ]; then
                print_error "CloudFront 分发 $DISTRIBUTION_ID 不存在，跳过缓存失效"
                print_warning "可能原因:"
                print_warning "1. 部署过程中 CloudFormation 堆栈创建失败"
                print_warning "2. 部署尚未完成"
                print_warning "3. 分发 ID 不正确"
            else
                INVALIDATION_ID=$(aws cloudfront create-invalidation \
                    --distribution-id $DISTRIBUTION_ID \
                    --paths "/*" \
                    --query 'Invalidation.Id' \
                    --output text)
                
                print_success "已创建缓存失效 (ID: $INVALIDATION_ID)"
                print_status "缓存失效可能需要 5-15 分钟完成"
            fi
            
            # 保存部署记录
            DEPLOY_RECORD="{\"id\":\"$DEPLOYMENT_ID\",\"timestamp\":\"$(date -u +"%Y-%m-%dT%H:%M:%SZ")\",\"environment\":\"$ENVIRONMENT\",\"url\":\"$CLOUDFRONT_URL\",\"invalidation\":\"$INVALIDATION_ID\"}"
            mkdir -p .deploy-history
            echo $DEPLOY_RECORD > ".deploy-history/deploy-$DEPLOYMENT_ID.json"
            print_status "部署记录已保存到 .deploy-history/deploy-$DEPLOYMENT_ID.json"
        fi
    fi
else
    print_error "AWS 部署失败"
    print_status "查看日志文件了解详情: $LOG_FILE"
    exit 1
fi

print_step "验证部署..."
if curl -s -o /dev/null -w "%{http_code}" $CLOUDFRONT_URL | grep -q "200"; then
    print_success "✅ 应用可访问并返回 HTTP 200"
else
    print_warning "⚠️ 应用可能尚未完全可访问。请手动检查。"
fi

print_success "==================================================="
print_success "🚀 部署完成！应用现已上线："
print_success "$CLOUDFRONT_URL"
print_success "==================================================="
print_status "查看部署日志: cat $LOG_FILE"