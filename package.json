{"name": "time_track_app2", "version": "0.1.0", "description": "Time tracking app with Eisenhower matrix prioritization", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "deploy:local": "./scripts/deploy-local.sh", "deploy:docker": "./scripts/deploy-docker.sh", "deploy:aws": "./scripts/aws-deploy.sh", "deploy:aws:status": "./scripts/aws-status.sh", "deploy:aws:prereqs": "./scripts/aws-prereqs.sh", "clean:build": "./scripts/clean-build-dirs.sh", "fix:next": "./scripts/fix-next-dir.sh", "force:clean:next": "./scripts/force-clean-next.sh"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@tailwindcss/postcss": "^4.1.12", "@types/node": "^24.3.0", "@types/react": "^19.1.11", "@types/react-dom": "^19.1.7", "autoprefixer": "^10.4.21", "chart.js": "^4.5.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "eslint": "^9.34.0", "eslint-config-next": "^15.5.0", "lucide-react": "^0.541.0", "next": "^15.5.0", "postcss": "^8.5.6", "react": "^19.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.1", "tailwindcss": "^4.1.12", "typescript": "^5.9.2"}, "devDependencies": {"@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "ts-jest": "^29.4.1"}}