(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2855:(e,t,r)=>{Promise.resolve().then(r.bind(r,7040))},7040:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eo});var a=r(5155),s=r(2115),n=r(5194);function l(e){let{size:t="md",className:r}=e;return(0,a.jsx)("div",{className:(0,n.cn)("loading-spinner",{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[t],r)})}let i=(0,s.forwardRef)((e,t)=>{let{className:r,variant:s="primary",size:i="md",isLoading:o,children:c,disabled:d,...m}=e;return(0,a.jsxs)("button",{ref:t,className:(0,n.cn)("inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-500 hover:bg-primary-600 text-white focus:ring-primary-500",secondary:"bg-gray-200 hover:bg-gray-300 text-gray-800 focus:ring-gray-500",danger:"bg-red-500 hover:bg-red-600 text-white focus:ring-red-500",ghost:"hover:bg-gray-100 text-gray-700 focus:ring-gray-500"}[s],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[i],r),disabled:d||o,...m,children:[o&&(0,a.jsx)(l,{size:"sm",className:"mr-2"}),c]})});i.displayName="Button";var o=r(3086),c=r(9867);function d(){let{state:e}=(0,o.A)(),[t,r]=(0,s.useState)(!1),l=async()=>{r(!0);try{let t=(0,n._2)(e.currentWeek).map(n.bU),r=e.timeEntries.filter(e=>t.includes(e.date)).map(t=>{var r,a;let s=e.categories.find(e=>e.id===t.categoryId),l=new Date(t.date);return{Date:t.date,Day:l.toLocaleDateString("en-US",{weekday:"long"}),Time:(0,n.KY)(t.hour),Category:(null==s?void 0:s.name)||"Unknown",Important:t.isImportant?"Yes":"No",Urgent:t.isUrgent?"Yes":"No",Quadrant:(r=t.isImportant,a=t.isUrgent,r&&a?"Q1 (Do First)":r&&!a?"Q2 (Schedule)":!r&&a?"Q3 (Delegate)":"Q4 (Eliminate)"),Description:t.description||""}});r.sort((e,t)=>{let r=e.Date.localeCompare(t.Date);return 0!==r?r:e.Time.localeCompare(t.Time)});let a=Object.keys(r[0]||{}),s=[a.join(","),...r.map(e=>a.map(t=>{let r=e[t];return'"'.concat(String(r).replace(/"/g,'""'),'"')}).join(","))].join("\n"),l=new Blob([s],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a"),o=URL.createObjectURL(l);i.setAttribute("href",o),i.setAttribute("download","time-tracking-".concat((0,n.bU)(e.currentWeek),".csv")),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(o)}catch(e){console.error("Failed to export CSV:",e),alert("Failed to export data. Please try again.")}finally{r(!1)}};return(0,a.jsxs)(i,{onClick:l,variant:"secondary",size:"sm",isLoading:t,className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Export CSV"]})}var m=r(2192),x=r(851),g=r(3796),u=r(7326),h=r(6485),y=r(4033);function p(e){let{value:t,onChange:r,className:l}=e,[i,o]=(0,s.useState)(!1),[c,d]=(0,s.useState)(t),p=(0,s.useRef)(null);(0,s.useEffect)(()=>{function e(e){p.current&&!p.current.contains(e.target)&&o(!1)}return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let b=e=>{let t=(0,m.k)(e,{weekStartsOn:1});d(t),r(t),o(!1)},f=e=>{let t=new Date(c);t.setMonth(c.getMonth()+("next"===e?1:-1)),d(t)},j=e=>{let t=new Date;return e.toDateString()===t.toDateString()},N=e=>{let r=(0,m.k)(e,{weekStartsOn:1}),a=(0,m.k)(t,{weekStartsOn:1});return r.getTime()===a.getTime()};return(0,a.jsxs)("div",{className:(0,n.cn)("relative",l),ref:p,children:[(0,a.jsxs)("button",{onClick:()=>o(!i),className:"flex items-center space-x-2 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 text-gray-500"}),(0,a.jsxs)("span",{className:"text-gray-700",children:["Week of ",(0,u.GP)((0,m.k)(t,{weekStartsOn:1}),"MMM d, yyyy")]}),(0,a.jsx)(y.A,{className:(0,n.cn)("h-4 w-4 text-gray-400 transition-transform",i&&"rotate-180")})]}),i&&(0,a.jsxs)("div",{className:"absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[320px]",children:[(0,a.jsxs)("div",{className:"p-3 border-b border-gray-200",children:[(0,a.jsx)("h4",{className:"text-xs font-medium text-gray-500 mb-2",children:"Quick Select"}),(0,a.jsx)("div",{className:"space-y-1",children:(()=>{let e=new Date,t=(0,m.k)(e,{weekStartsOn:1});return[{label:"This Week",date:t},{label:"Next Week",date:(0,x.J)(t,1)},{label:"Last Week",date:(0,g.k)(t,1)},{label:"Next Month",date:(0,x.J)(t,4)},{label:"Last Month",date:(0,g.k)(t,4)}]})().map(e=>(0,a.jsx)("button",{onClick:()=>b(e.date),className:"w-full text-left px-2 py-1 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors",children:e.label},e.label))})]}),(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("button",{onClick:()=>f("prev"),className:"p-1 hover:bg-gray-100 rounded transition-colors",children:(0,a.jsx)(y.A,{className:"h-4 w-4 rotate-90"})}),(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:(0,u.GP)(c,"MMMM yyyy")}),(0,a.jsx)("button",{onClick:()=>f("next"),className:"p-1 hover:bg-gray-100 rounded transition-colors",children:(0,a.jsx)(y.A,{className:"h-4 w-4 -rotate-90"})})]}),(0,a.jsx)("div",{className:"grid grid-cols-7 gap-1 mb-2",children:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"].map(e=>(0,a.jsx)("div",{className:"text-xs font-medium text-gray-500 text-center py-1",children:e},e))}),(0,a.jsx)("div",{className:"grid grid-cols-7 gap-1",children:(()=>{let e=c.getMonth(),t=new Date(c.getFullYear(),e,1),r=(0,m.k)(t,{weekStartsOn:1}),a=[];for(let e=0;e<42;e++){let t=new Date(r);t.setDate(r.getDate()+e),a.push(t)}return a})().map((e,t)=>(0,a.jsx)("button",{onClick:()=>b(e),className:(0,n.cn)("text-sm p-2 rounded transition-colors",e.getMonth()===c.getMonth()?"text-gray-900":"text-gray-400",j(e)&&"bg-primary-100 text-primary-700 font-medium",N(e)&&"bg-primary-500 text-white",!N(e)&&!j(e)&&"hover:bg-gray-100"),children:e.getDate()},t))})]})]})]})}var b=r(6983),f=r(368),j=r(7937),N=r(1360);function v(){let{state:e,actions:t}=(0,o.A)(),[r,l]=(0,s.useState)(!1),c=async()=>{if(confirm("Are you sure you want to clear all time entries for this week? This action cannot be undone.")){l(!0);try{await t.clearWeekData(e.currentWeek)}catch(e){console.error("Failed to clear week data:",e)}finally{l(!1)}}};return(0,a.jsx)("header",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-primary-500 rounded-lg",children:(0,a.jsx)(b.A,{className:"h-6 w-6 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Time Tracker"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Eisenhower Matrix"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(i,{variant:"ghost",size:"sm",onClick:()=>{let r=(0,n.oE)(e.currentWeek);t.setCurrentWeek(r)},className:"p-2",title:"Previous Week",children:(0,a.jsx)(f.A,{className:"h-4 w-4"})}),(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:(0,n.k2)(e.currentWeek)}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(i,{variant:"ghost",size:"sm",onClick:()=>{t.setCurrentWeek((0,n.lV)())},className:"text-xs text-primary-600 hover:text-primary-700",children:"Today"}),(0,a.jsx)(p,{value:e.currentWeek,onChange:t.setCurrentWeek,className:"text-xs"})]})]}),(0,a.jsx)(i,{variant:"ghost",size:"sm",onClick:()=>{let r=(0,n.Y6)(e.currentWeek);t.setCurrentWeek(r)},className:"p-2",title:"Next Week",children:(0,a.jsx)(j.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(i,{variant:"ghost",size:"sm",onClick:c,disabled:r,className:"text-red-600 hover:text-red-700 hover:bg-red-50",title:"Clear all entries for this week",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2"}),r?"Clearing...":"Clear Week"]}),(0,a.jsx)(d,{})]})]})})}var w=r(8566);function k(e){let{onSuccess:t,onCancel:r,initialData:n}=e,{actions:l}=(0,o.A)(),[c,d]=(0,s.useState)({name:(null==n?void 0:n.name)||"",color:(null==n?void 0:n.color)||w.Gv[0]}),[m,x]=(0,s.useState)(!1),[g,u]=(0,s.useState)(null),h=!!n,y=async e=>{if(e.preventDefault(),!c.name.trim())return void u("Category name is required");x(!0),u(null);try{h?await l.updateCategory(n.id,c):await l.createCategory(c),d({name:"",color:"#3B82F6"}),t()}catch(e){u(e instanceof Error?e.message:"Failed to save category")}finally{x(!1)}};return(0,a.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[(0,a.jsx)("div",{children:(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:h?"Edit Category":"Add New Category"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"categoryName",className:"block text-xs font-medium text-gray-700 mb-1",children:"Name"}),(0,a.jsx)("input",{id:"categoryName",type:"text",value:c.name,onChange:e=>d({...c,name:e.target.value}),placeholder:"e.g., Work/Coding",className:"input-field text-sm",maxLength:50,required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-gray-700 mb-2",children:"Color"}),(0,a.jsx)("div",{className:"grid grid-cols-6 gap-2",children:w.Gv.map(e=>(0,a.jsx)("button",{type:"button",onClick:()=>d({...c,color:e}),className:"w-8 h-8 rounded-lg border-2 transition-all ".concat(c.color===e?"border-gray-900 scale-110":"border-gray-300 hover:border-gray-400"),style:{backgroundColor:e},title:e},e))})]}),g&&(0,a.jsx)("div",{className:"text-xs text-red-600 bg-red-50 p-2 rounded",children:g}),(0,a.jsxs)("div",{className:"flex space-x-2 pt-2",children:[(0,a.jsxs)(i,{type:"submit",size:"sm",isLoading:m,className:"flex-1",children:[h?"Update":"Add"," Category"]}),(0,a.jsx)(i,{type:"button",variant:"secondary",size:"sm",onClick:r,disabled:m,children:"Cancel"})]})]})}var C=r(1190);function S(){let{state:e,actions:t}=(0,o.A)(),[r,n]=(0,s.useState)(null),[l,c]=(0,s.useState)(null),d=async e=>{try{await t.deleteCategory(e),c(null)}catch(e){console.error("Failed to delete category:",e)}},m=()=>{n(null)};return 0===e.categories.length?(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("p",{className:"text-sm",children:"No categories yet"}),(0,a.jsx)("p",{className:"text-xs mt-1",children:"Add your first category to get started"})]}):(0,a.jsxs)("div",{className:"space-y-2",children:[e.categories.map(e=>(0,a.jsx)("div",{children:r===e.id?(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-3 bg-gray-50",children:(0,a.jsx)(k,{initialData:e,onSuccess:m,onCancel:()=>n(null)})}):(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center flex-1 min-w-0",children:[(0,a.jsx)("div",{className:"category-color-dot flex-shrink-0",style:{backgroundColor:e.color}}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900 truncate",children:e.name})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1 ml-2",children:[(0,a.jsx)("button",{onClick:()=>{n(e.id)},className:"p-1 text-gray-400 hover:text-gray-600 transition-colors",title:"Edit category",children:(0,a.jsx)(C.A,{className:"h-3 w-3"})}),(0,a.jsx)("button",{onClick:()=>c(e.id),className:"p-1 text-gray-400 hover:text-red-600 transition-colors",title:"Delete category",children:(0,a.jsx)(N.A,{className:"h-3 w-3"})})]})]})},e.id)),l&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-sm w-full p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Delete Category"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Are you sure you want to delete this category? This action cannot be undone."}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(i,{variant:"danger",onClick:()=>d(l),className:"flex-1",children:"Delete"}),(0,a.jsx)(i,{variant:"secondary",onClick:()=>c(null),className:"flex-1",children:"Cancel"})]})]})})]})}let D=(0,s.createContext)(void 0);function A(e){let{children:t}=e,[r,n]=(0,s.useState)("categories");return(0,a.jsx)(D.Provider,{value:{activeTab:r,setActiveTab:n},children:t})}function E(){let e=(0,s.useContext)(D);if(void 0===e)throw Error("useSidebar must be used within a SidebarProvider");return e}var I=r(814),T=r(1524),F=r(1607),U=r(877);function W(e){let{timeEntries:t,categories:r,className:n="",onCategoryClick:l,containerWidth:i=320,selectedCategories:o=[]}=e,d=(0,s.useRef)(null),m=(0,s.useMemo)(()=>{let e={};t.forEach(t=>{e[t.categoryId]=(e[t.categoryId]||0)+1});let a=r.filter(t=>e[t.id]>0);if(o.length>0&&(a=a.filter(e=>o.includes(e.id))),0===a.length)return null;let s=a.map(e=>e.name),n=a.map(t=>e[t.id]),l=a.map(e=>e.color),i=a.map(e=>e.color),c=a.map(e=>e.id);return{labels:s,datasets:[{label:"Hours",data:n,backgroundColor:l.map(e=>e+"80"),borderColor:i,borderWidth:2,hoverBackgroundColor:l,hoverBorderWidth:3,categoryIds:c}]}},[t,r,o]),x=(0,s.useMemo)(()=>({responsive:!0,maintainAspectRatio:!1,animation:{duration:i<300?0:750},onClick:(e,t)=>{if(t.length>0&&l&&m){let e=t[0].index;l(m.datasets[0].categoryIds[e])}},plugins:{legend:{position:"bottom",align:"center",labels:{padding:i<350?12:20,usePointStyle:!0,font:{size:i<350?11:12,family:"system-ui"},boxWidth:i<350?30:40},maxWidth:i-40},tooltip:{callbacks:{label:function(e){let t=e.label||"",r=e.parsed,a=(r/e.dataset.data.reduce((e,t)=>e+t,0)*100).toFixed(1);return"".concat(t,": ").concat(r,"h (").concat(a,"%) - Click to filter")}},padding:i<350?8:12,bodyFont:{size:i<350?11:13}}}}),[i,l,m]);if(!m)return(0,a.jsx)("div",{className:"flex items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 ".concat(n),children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-2",children:"\uD83D\uDCCA"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"No time entries yet"}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:"Add some time entries to see the distribution"})]})});let g=o.length>0;return(0,a.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-4 ".concat(n),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h3",{className:"text-sm font-medium text-gray-900",children:["Category Distribution",g&&(0,a.jsxs)("span",{className:"ml-2 text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded",children:["Filtered (",o.length," categories)"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[m&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("button",{onClick:()=>{if(d.current){let e=d.current.toBase64Image(),t=document.createElement("a");t.download="category-distribution-chart.png",t.href=e,t.click()}},className:"p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded",title:"Export chart as PNG",children:(0,a.jsx)(c.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>{if(!m)return;let e=new Blob([["Category,Hours,Color",...m.labels.map((e,t)=>({Category:e,Hours:m.datasets[0].data[t],Color:m.datasets[0].borderColor[t]})).map(e=>[e.Category,e.Hours,e.Color].join(","))].join("\n")],{type:"text/csv"}),t=URL.createObjectURL(e),r=document.createElement("a");r.download="category-distribution-data.csv",r.href=t,r.click(),URL.revokeObjectURL(t)},className:"px-2 py-1 text-xs text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded",title:"Export data as CSV",children:"CSV"})]}),g&&l&&(0,a.jsx)("button",{onClick:()=>{o.forEach(e=>l(e))},className:"text-xs text-gray-500 hover:text-gray-700 underline",children:"Clear filters"})]})]}),(0,a.jsx)("div",{className:"transition-all duration-300",style:{height:i<300?"200px":i<400?"240px":"280px"},children:(0,a.jsx)(U.Fq,{ref:d,data:m,options:x})})]})}function R(e){let{timeEntries:t,className:r="",containerWidth:l=350}=e,i=(0,s.useRef)(null),o=(0,s.useMemo)(()=>{let e={q1:{count:0,label:"Q1: Do First (Important + Urgent)",symbol:"\uD83D\uDD25",color:"#ef4444"},q2:{count:0,label:"Q2: Schedule (Important)",symbol:"⭐",color:"#10b981"},q3:{count:0,label:"Q3: Delegate (Urgent)",symbol:"⚡",color:"#f59e0b"},q4:{count:0,label:"Q4: Eliminate (Neither)",symbol:"\uD83D\uDCA4",color:"#6b7280"}};if(t.forEach(t=>{t.isImportant&&t.isUrgent?e.q1.count++:t.isImportant&&!t.isUrgent?e.q2.count++:!t.isImportant&&t.isUrgent?e.q3.count++:e.q4.count++}),0===t.length)return null;let r=Object.values(e).filter(e=>e.count>0);return{labels:r.map(e=>"".concat(e.symbol," ").concat(e.label)),datasets:[{data:r.map(e=>e.count),backgroundColor:r.map(e=>e.color+"80"),borderColor:r.map(e=>e.color),borderWidth:2,hoverBackgroundColor:r.map(e=>e.color),hoverBorderWidth:3}]}},[t]),d=(0,s.useMemo)(()=>({responsive:!0,maintainAspectRatio:!1,animation:{duration:l<350?0:750},plugins:{legend:{position:"bottom",align:"center",labels:{usePointStyle:!0,font:{size:l<350?11:12,family:"system-ui"},boxWidth:l<350?30:40,padding:l<350?12:20},maxWidth:l-40},tooltip:{callbacks:{label:function(e){let t=e.label||"",r=e.parsed,a=(r/e.dataset.data.reduce((e,t)=>e+t,0)*100).toFixed(1);return"".concat(t,": ").concat(r,"h (").concat(a,"%)")}},padding:l<350?8:12,bodyFont:{size:l<350?11:13}}}}),[l]);return o?(0,a.jsx)("div",{className:(0,n.cn)("bg-white p-4 rounded-lg shadow",r),children:(0,a.jsxs)("div",{className:(0,n.cn)("space-y-4",l<350&&"space-y-3"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between flex-wrap gap-2",children:[(0,a.jsx)("h3",{className:(0,n.cn)("font-medium text-gray-900",l<350?"text-xs":"text-sm"),children:"Priority Distribution"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{className:(0,n.cn)("text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 transition-colors",l<350?"p-1":"p-1.5"),onClick:()=>{if(i.current){let e=i.current.toBase64Image(),t=document.createElement("a");t.download="priority-distribution-chart.png",t.href=e,t.click()}},title:"Export chart as PNG",children:(0,a.jsx)(c.A,{className:(0,n.cn)("w-4 h-4",l<350&&"w-3.5 h-3.5")})}),(0,a.jsx)("button",{className:(0,n.cn)("text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 transition-colors",l<350?"p-1":"p-1.5"),onClick:()=>{if(!(null==o?void 0:o.labels))return;let e=o.datasets[0].data,t=e.reduce((e,t)=>e+t,0),r=new Blob([["Quadrant,Hours,Percentage",...o.labels.map((r,a)=>({Quadrant:r,Hours:e[a],Percentage:(e[a]/t*100).toFixed(1)})).map(e=>[e.Quadrant,e.Hours,e.Percentage].join(","))].join("\n")],{type:"text/csv"}),a=URL.createObjectURL(r),s=document.createElement("a");s.download="priority-distribution-data.csv",s.href=a,s.click(),URL.revokeObjectURL(a)},title:"Export data as CSV",children:"CSV"})]})]}),(0,a.jsx)("div",{style:{height:l<350?"200px":l<450?"240px":"280px"},children:(0,a.jsx)(U.Fq,{ref:i,data:o,options:d})})]})}):(0,a.jsx)("div",{className:(0,n.cn)("flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300",l<350?"h-48":"h-64",r),children:(0,a.jsxs)("div",{className:"text-center px-4",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-2",children:"\uD83D\uDCCA"}),(0,a.jsx)("p",{className:(0,n.cn)("text-gray-500 mb-1",l<350?"text-xs":"text-sm"),children:"No time entries yet"}),(0,a.jsx)("p",{className:(0,n.cn)("text-gray-400",l<350?"text-[10px]":"text-xs"),children:"Add some time entries to see the priority distribution"})]})})}F.t1.register(F.Bs,F.m_,F.s$),F.t1.register(F.Bs,F.m_,F.s$);var L=r(2381);function O(e){var t;let{timeEntries:r,currentWeek:l,className:i="",containerWidth:o=320}=e,d=(0,s.useMemo)(()=>{let e=(0,m.k)(l,{weekStartsOn:1}),t={},a=0;for(let s=0;s<7;s++){let l=(0,L.f)(e,s),i=(0,n.bU)(l),o=r.filter(e=>e.date===i);t[i]=o.length,a+=o.length}let s=Math.round(a/98*100);return{dailyHours:t,totalHours:a,totalPossibleHours:98,completionPercentage:s,weekStart:e}},[r,l]),x=e=>(0,n.uA)(e);return(0,a.jsx)("div",{className:(0,n.cn)("bg-white p-4 rounded-lg shadow",i),children:(0,a.jsxs)("div",{className:(0,n.cn)("space-y-4",o<350&&"space-y-3"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between flex-wrap gap-2",children:[(0,a.jsx)("h3",{className:(0,n.cn)("font-medium text-gray-900",o<350?"text-xs":"text-sm"),children:"Weekly Progress"}),(0,a.jsx)("button",{className:(0,n.cn)("p-1.5 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 transition-colors",o<350&&"p-1"),onClick:()=>{let e=new Blob([["Date,Day,Hours,MaxHours,Percentage",...Array.from({length:7},(e,t)=>{let r=(0,L.f)(d.weekStart,t),a=(0,n.bU)(r),s=d.dailyHours[a]||0;return{Date:a,Day:x(r),Hours:s,MaxHours:14,Percentage:(s/14*100).toFixed(1)}}).map(e=>[e.Date,e.Day,e.Hours,e.MaxHours,e.Percentage].join(","))].join("\n")],{type:"text/csv"}),t=URL.createObjectURL(e),r=document.createElement("a");r.download="weekly-progress-data.csv",r.href=t,r.click(),URL.revokeObjectURL(t)},title:"Export data as CSV",children:(0,a.jsx)(c.A,{className:(0,n.cn)("w-4 h-4",o<350&&"w-3.5 h-3.5")})})]}),(0,a.jsxs)("div",{className:(0,n.cn)("mb-6",o<350&&"mb-4"),children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("span",{className:(0,n.cn)("text-gray-600",o<350?"text-xs":"text-sm"),children:"Overall Completion"}),(0,a.jsx)("span",{className:(0,n.cn)("font-medium text-gray-900",o<350?"text-xs":"text-sm"),children:"".concat(d.totalHours,"h / ").concat(d.totalPossibleHours,"h (").concat(d.completionPercentage,"%)")})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,a.jsx)("div",{className:(0,n.cn)("rounded-full transition-all duration-300 h-2.5",(t=d.completionPercentage)>=80?"bg-green-500":t>=60?"bg-yellow-500":t>=40?"bg-orange-500":"bg-red-500"),style:{width:"".concat(Math.min(d.completionPercentage,100),"%")}})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:(0,n.cn)("font-medium text-gray-700 mb-3",o<350?"text-xs mb-2":"text-sm"),children:"Daily Breakdown"}),(0,a.jsx)("div",{className:"grid grid-cols-7 gap-1.5",children:Array.from({length:7},(e,t)=>{let r=(0,L.f)(d.weekStart,t),s=(0,n.bU)(r),l=d.dailyHours[s]||0,i=l/14*100;return(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:(0,n.cn)("text-gray-500 mb-1",o<350?"text-[10px]":"text-xs"),children:x(r)}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:(0,n.cn)("w-full bg-gray-200 rounded flex items-end",o<350?"h-12":"h-16"),children:(0,a.jsx)("div",{className:(0,n.cn)("w-full rounded transition-all duration-300",(e=>{let t=e/14*100;return t>=80?"bg-green-400":t>=60?"bg-yellow-400":t>=40?"bg-orange-400":t>0?"bg-red-400":"bg-gray-200"})(l)),style:{height:"".concat(Math.max(i,5),"%")}})}),(0,a.jsx)("div",{className:(0,n.cn)("text-gray-600 mt-1",o<350?"text-[10px]":"text-xs"),children:"".concat(l,"h")})]})]},s)})})]}),(0,a.jsx)("div",{className:(0,n.cn)("mt-4 pt-4 border-t border-gray-100",o<350&&"mt-3 pt-3"),children:(0,a.jsxs)("div",{className:(0,n.cn)("flex items-center justify-between text-gray-500",o<350?"text-[10px]":"text-xs"),children:[(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-2 md:gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-2.5 h-2.5 bg-green-400 rounded"}),(0,a.jsx)("span",{children:"80%+"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-2.5 h-2.5 bg-yellow-400 rounded"}),(0,a.jsx)("span",{children:"60-79%"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-2.5 h-2.5 bg-orange-400 rounded"}),(0,a.jsx)("span",{children:"40-59%"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-2.5 h-2.5 bg-red-400 rounded"}),(0,a.jsx)("span",{children:"<40%"})]})]}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Daily completion rate"})]})})]})})}var P=r(7828),M=r(532);function z(e){let{categories:t,selectedCategories:r,onToggleCategory:s,onSelectAll:n,onSelectNone:l,className:i=""}=e,o=0===r.length,c=t.length>0&&r.length===t.length,d=!o&&!c,m=()=>{n()};return(0,a.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-4 ".concat(i),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Category Visibility"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:m,disabled:o,className:"text-xs text-blue-600 hover:text-blue-800 disabled:text-gray-400 disabled:cursor-not-allowed",children:"Show All"}),(0,a.jsx)("span",{className:"text-xs text-gray-300",children:"|"}),(0,a.jsx)("button",{onClick:()=>{l()},disabled:c,className:"text-xs text-blue-600 hover:text-blue-800 disabled:text-gray-400 disabled:cursor-not-allowed",children:"Hide All"})]})]}),0===t.length?(0,a.jsx)("p",{className:"text-xs text-gray-500 text-center py-4",children:"No categories available"}):(0,a.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:t.map(e=>{let t,n=(t=e.id,!r.includes(t));return(0,a.jsxs)("div",{className:"flex items-center gap-3 p-2 rounded hover:bg-gray-50 transition-colors",children:[(0,a.jsx)("button",{onClick:()=>s(e.id),className:"flex items-center justify-center w-5 h-5 rounded border-2 transition-colors ".concat(n?"bg-blue-50 border-blue-500 text-blue-600":"bg-gray-50 border-gray-300 text-gray-400"),children:n?(0,a.jsx)(P.A,{className:"w-3 h-3"}):(0,a.jsx)(M.A,{className:"w-3 h-3"})}),(0,a.jsx)("div",{className:"w-3 h-3 rounded-full border border-gray-300 flex-shrink-0",style:{backgroundColor:e.color}}),(0,a.jsx)("span",{className:"text-sm flex-1 transition-colors ".concat(n?"text-gray-900":"text-gray-400"),children:e.name}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:n?"Visible":"Hidden"})]},e.id)})}),(0,a.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:[o&&"All categories visible",d&&"".concat(t.length-r.length," of ").concat(t.length," visible"),c&&"No categories visible"]}),(d||c)&&(0,a.jsx)("button",{onClick:m,className:"text-blue-600 hover:text-blue-800 underline",children:"Reset"})]})})]})}var _=r(5229);function H(e){let{startDate:t,endDate:r,onDateRangeChange:n,onClear:l,className:i=""}=e,[o,c]=(0,s.useState)(!1),[d,m]=(0,s.useState)((null==t?void 0:t.toISOString().split("T")[0])||""),[x,g]=(0,s.useState)((null==r?void 0:r.toISOString().split("T")[0])||""),u=t&&r,y=()=>{m(""),g(""),l(),c(!1)};return(0,a.jsxs)("div",{className:"relative ".concat(i),children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("button",{onClick:()=>c(!o),className:"flex items-center gap-2 px-3 py-2 text-sm border rounded-md transition-colors ".concat(u?"bg-blue-50 border-blue-200 text-blue-700":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"," ").concat(u?"rounded-r-none border-r-0":""),children:[(0,a.jsx)(h.A,{className:"w-4 h-4"}),u?(0,a.jsxs)("span",{children:[null==t?void 0:t.toLocaleDateString()," - ",null==r?void 0:r.toLocaleDateString()]}):(0,a.jsx)("span",{children:"Select date range"})]}),u&&(0,a.jsx)("button",{onClick:y,className:"px-2 py-2 text-sm bg-blue-50 border border-blue-200 border-l-0 text-blue-700 hover:bg-blue-100 rounded-r-md",children:(0,a.jsx)(_.A,{className:"w-3 h-3"})})]}),o&&(0,a.jsx)("div",{className:"absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 p-4 min-w-[300px]",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"start-date",className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),(0,a.jsx)("input",{id:"start-date",type:"date",value:d,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"end-date",className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),(0,a.jsx)("input",{id:"end-date",type:"date",value:x,onChange:e=>g(e.target.value),min:d,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Quick Select"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsx)("button",{onClick:()=>{let e=new Date,t=new Date(e);t.setDate(e.getDate()-7),m(t.toISOString().split("T")[0]),g(e.toISOString().split("T")[0])},className:"px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded",children:"Last 7 days"}),(0,a.jsx)("button",{onClick:()=>{let e=new Date,t=new Date(e);t.setDate(e.getDate()-30),m(t.toISOString().split("T")[0]),g(e.toISOString().split("T")[0])},className:"px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded",children:"Last 30 days"}),(0,a.jsx)("button",{onClick:()=>{let e=new Date;m(new Date(e.getFullYear(),e.getMonth(),1).toISOString().split("T")[0]),g(e.toISOString().split("T")[0])},className:"px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded",children:"This month"}),(0,a.jsx)("button",{onClick:()=>{let e=new Date,t=new Date(e.getFullYear(),e.getMonth()-1,1),r=new Date(e.getFullYear(),e.getMonth(),0);m(t.toISOString().split("T")[0]),g(r.toISOString().split("T")[0])},className:"px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded",children:"Last month"})]})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-2 border-t border-gray-200",children:[(0,a.jsx)("button",{onClick:y,className:"px-3 py-1 text-sm text-gray-600 hover:text-gray-800",children:"Clear"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>{m((null==t?void 0:t.toISOString().split("T")[0])||""),g((null==r?void 0:r.toISOString().split("T")[0])||""),c(!1)},className:"px-3 py-1 text-sm text-gray-600 hover:text-gray-800",children:"Cancel"}),(0,a.jsx)("button",{onClick:()=>{if(d&&x){let e=new Date(d),t=new Date(x);e<=t&&(n(e,t),c(!1))}},disabled:!d||!x,className:"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed",children:"Apply"})]})]})]})}),o&&(0,a.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>c(!1)})]})}var Q=r(9476);function G(e){let{children:t,defaultWidth:r=350,minWidth:l=250,maxWidth:i=500,className:o,onResize:c,storageKey:d="resizable-panel-width"}=e,[m,x]=(0,s.useState)(r),[g,u]=(0,s.useState)(!1),h=(0,s.useRef)(null),y=(0,s.useRef)(0),p=(0,s.useRef)(0);(0,s.useEffect)(()=>{{let e=localStorage.getItem(d);if(e){let t=parseInt(e,10);t>=l&&t<=i&&x(t)}}},[d,l,i]),(0,s.useEffect)(()=>{localStorage.setItem(d,m.toString()),null==c||c(m)},[m,d,c]);let b=(0,s.useCallback)(e=>{e.preventDefault(),y.current=e.clientX,p.current=m,u(!0),document.body.style.cursor="col-resize",document.body.style.userSelect="none",document.body.classList.add("resize-active")},[m]);return(0,s.useEffect)(()=>{if(!g)return;let e=e=>{e.preventDefault();let t=e.clientX-y.current;x(Math.max(l,Math.min(i,p.current+t)))},t=()=>{u(!1),document.body.style.cursor="",document.body.style.userSelect="",document.body.classList.remove("resize-active")};return document.addEventListener("mousemove",e),document.addEventListener("mouseup",t),()=>{document.removeEventListener("mousemove",e),document.removeEventListener("mouseup",t)}},[g,l,i]),(0,a.jsx)("div",{className:"flex h-full",children:(0,a.jsxs)("div",{ref:h,className:(0,n.cn)("relative bg-white border-r border-gray-200 h-full overflow-hidden",!g&&"transition-all duration-300 ease-in-out",o),style:{width:"".concat(m,"px")},children:[(0,a.jsx)("div",{className:"h-full overflow-y-auto custom-scrollbar",children:t}),(0,a.jsx)("div",{role:"separator","aria-label":"Resize panel","aria-valuenow":m,"aria-valuemin":l,"aria-valuemax":i,className:(0,n.cn)("absolute top-0 right-0 w-4 h-full cursor-col-resize group transition-colors z-20",'after:content-[""] after:absolute after:top-0 after:left-1/2 after:w-px after:h-full',"after:bg-gray-300 after:transition-opacity after:duration-200",g?"after:opacity-100":"after:opacity-60 hover:after:opacity-100"),onMouseDown:b,children:(0,a.jsx)("div",{className:(0,n.cn)("absolute top-1/2 right-0 transform -translate-y-1/2 translate-x-1/2","transition-all duration-200",g&&"scale-110"),children:(0,a.jsx)("div",{className:(0,n.cn)("flex items-center justify-center w-6 h-16 bg-white border border-gray-300 rounded-md","shadow-md hover:shadow-lg transition-all duration-200","opacity-80 group-hover:opacity-100",g&&"opacity-100 shadow-xl border-blue-500 bg-blue-50"),children:(0,a.jsx)(Q.A,{className:(0,n.cn)("h-4 w-4 transition-colors duration-200",g?"text-primary-500":"text-gray-400 group-hover:text-gray-500")})})})})]})})}function B(){var e,t;let{state:r,actions:l}=(0,o.A)(),{activeTab:i}=E(),c=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"resizable-panel-width",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:320,[r,a]=(0,s.useState)(t);return(0,s.useEffect)(()=>{{let t=localStorage.getItem(e);t&&a(parseInt(t,10))}let t=t=>{t.key===e&&t.newValue&&a(parseInt(t.newValue,10))};return window.addEventListener("storage",t),()=>window.removeEventListener("storage",t)},[e]),r}("stats"===i?"stats-panel-width":"sidebar-width","stats"===i?450:320),d=(0,s.useMemo)(()=>{let e=(0,n._2)(r.currentWeek).map(n.bU),t=r.timeEntries.filter(t=>e.includes(t.date)),{selectedCategories:a,dateRange:s}=r.chartFilters;return a.length>0&&(t=t.filter(e=>a.includes(e.categoryId))),s&&(t=t.filter(e=>{let t=new Date(e.date);return t>=s.startDate&&t<=s.endDate})),t},[r.timeEntries,r.currentWeek,r.chartFilters]),m=(0,s.useMemo)(()=>{var e;let t=(0,n._2)(r.currentWeek).map(n.bU),a=r.timeEntries.filter(e=>t.includes(e.date)),s=a.length,l=(0,n.$4)(s,w.r1),i=r.categories.map(e=>{let t=a.filter(t=>t.categoryId===e.id);return{category:e,hours:t.length,percentage:(0,n.$4)(t.length,s)}}).filter(e=>e.hours>0).sort((e,t)=>t.hours-e.hours),o={[w.eW.IMPORTANT_URGENT]:a.filter(e=>e.isImportant&&e.isUrgent).length,[w.eW.IMPORTANT_NOT_URGENT]:a.filter(e=>e.isImportant&&!e.isUrgent).length,[w.eW.NOT_IMPORTANT_URGENT]:a.filter(e=>!e.isImportant&&e.isUrgent).length,[w.eW.NOT_IMPORTANT_NOT_URGENT]:a.filter(e=>!e.isImportant&&!e.isUrgent).length},c=null==(e=i[0])?void 0:e.category;return{totalTracked:s,trackingPercentage:l,categoryStats:i,quadrantStats:o,mostUsedCategory:c}},[r.timeEntries,r.categories,r.currentWeek]);return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-primary-500 mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"This Week"})]}),(0,a.jsxs)("span",{className:"text-lg font-bold text-primary-600",children:[m.trackingPercentage,"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-primary-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(m.trackingPercentage,"%")}})}),(0,a.jsxs)("p",{className:"text-xs text-gray-600 mt-1",children:[m.totalTracked," of ",w.r1," hours tracked"]})]}),m.mostUsedCategory&&(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(0,a.jsx)(I.A,{className:"h-4 w-4 text-green-500 mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Top Category"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"category-color-dot",style:{backgroundColor:m.mostUsedCategory.color}}),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:m.mostUsedCategory.name})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-600 mt-1",children:[m.categoryStats[0].hours," hours (",m.categoryStats[0].percentage,"%)"]})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsxs)("div",{className:"flex items-center mb-3",children:[(0,a.jsx)(T.A,{className:"h-4 w-4 text-blue-500 mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Priority Matrix"})]}),(0,a.jsx)("div",{className:"space-y-2",children:Object.entries(m.quadrantStats).map(e=>{let[t,r]=e,s=(e=>{switch(e){case w.eW.IMPORTANT_URGENT:return{symbol:"\uD83D\uDD25",label:"Do First",color:"text-red-600 bg-red-50"};case w.eW.IMPORTANT_NOT_URGENT:return{symbol:"⭐",label:"Schedule",color:"text-green-600 bg-green-50"};case w.eW.NOT_IMPORTANT_URGENT:return{symbol:"⚡",label:"Delegate",color:"text-yellow-600 bg-yellow-50"};case w.eW.NOT_IMPORTANT_NOT_URGENT:return{symbol:"\uD83D\uDCA4",label:"Eliminate",color:"text-gray-600 bg-gray-50"}}})(t);return(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-sm mr-2",children:s.symbol}),(0,a.jsx)("span",{className:"text-xs text-gray-700",children:s.label})]}),(0,a.jsxs)("span",{className:"text-xs font-medium text-gray-900",children:[r,"h"]})]},t)})})]}),m.categoryStats.length>0&&(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsxs)("div",{className:"flex items-center mb-3",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 text-purple-500 mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Categories"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[m.categoryStats.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center flex-1 min-w-0",children:[(0,a.jsx)("div",{className:"category-color-dot flex-shrink-0",style:{backgroundColor:e.category.color}}),(0,a.jsx)("span",{className:"text-xs text-gray-700 truncate",children:e.category.name})]}),(0,a.jsxs)("span",{className:"text-xs font-medium text-gray-900 ml-2",children:[e.hours,"h"]})]},e.category.id)),m.categoryStats.length>5&&(0,a.jsxs)("p",{className:"text-xs text-gray-500 text-center pt-1",children:["+",m.categoryStats.length-5," more"]})]})]}),(0,a.jsxs)("div",{className:"card p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Chart Filters"}),(r.chartFilters.selectedCategories.length>0||r.chartFilters.dateRange)&&(0,a.jsx)("button",{onClick:l.clearAllFilters,className:"text-xs text-gray-500 hover:text-gray-700 underline",children:"Clear all filters"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-gray-700 mb-2",children:"Date Range Filter"}),(0,a.jsx)(H,{startDate:null==(e=r.chartFilters.dateRange)?void 0:e.startDate,endDate:null==(t=r.chartFilters.dateRange)?void 0:t.endDate,onDateRangeChange:l.setDateRangeFilter,onClear:l.clearDateRangeFilter})]}),(0,a.jsx)("div",{children:(0,a.jsx)(z,{categories:r.categories,selectedCategories:r.chartFilters.selectedCategories,onToggleCategory:l.toggleCategoryFilter,onSelectAll:l.clearAllFilters,onSelectNone:l.hideAllCategories})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(O,{timeEntries:d,currentWeek:r.currentWeek,containerWidth:c}),(0,a.jsx)(W,{timeEntries:d,categories:r.categories,onCategoryClick:l.toggleCategoryFilter,selectedCategories:r.chartFilters.selectedCategories,containerWidth:c}),(0,a.jsx)(R,{timeEntries:d,containerWidth:c})]})]})}function q(e){return 0===e.length}async function V(e,t,r,a){if(q(e))for(let e of function(e,t){let r=(0,n._2)(t),a=[];return[{day:0,hour:9,categoryName:"Work/Coding",important:!0,urgent:!1,description:"Sprint planning meeting"},{day:0,hour:10,categoryName:"Work/Coding",important:!0,urgent:!0,description:"Fix critical bug"},{day:0,hour:11,categoryName:"Work/Coding",important:!0,urgent:!0,description:"Code review"},{day:0,hour:14,categoryName:"Learning/Study",important:!0,urgent:!1,description:"AI LangGraph tutorial"},{day:0,hour:15,categoryName:"Learning/Study",important:!0,urgent:!1,description:"Practice coding"},{day:0,hour:19,categoryName:"Exercise/Health",important:!0,urgent:!1,description:"Gym workout"},{day:0,hour:21,categoryName:"Rest/Entertainment",important:!1,urgent:!1,description:"Watch Netflix"},{day:1,hour:9,categoryName:"Work/Coding",important:!0,urgent:!1,description:"Feature development"},{day:1,hour:10,categoryName:"Work/Coding",important:!0,urgent:!1,description:"Testing"},{day:1,hour:11,categoryName:"Work/Coding",important:!1,urgent:!0,description:"Team standup"},{day:1,hour:14,categoryName:"Learning/Study",important:!0,urgent:!1,description:"Read documentation"},{day:1,hour:18,categoryName:"Family/Social",important:!0,urgent:!1,description:"Dinner with family"},{day:1,hour:20,categoryName:"Exercise/Health",important:!0,urgent:!1,description:"Evening walk"},{day:2,hour:9,categoryName:"Work/Coding",important:!0,urgent:!0,description:"Client presentation"},{day:2,hour:10,categoryName:"Work/Coding",important:!0,urgent:!1,description:"Documentation"},{day:2,hour:11,categoryName:"Work/Coding",important:!1,urgent:!0,description:"Email responses"},{day:2,hour:15,categoryName:"Learning/Study",important:!0,urgent:!1,description:"Online course"},{day:2,hour:19,categoryName:"Family/Social",important:!0,urgent:!1,description:"Call parents"},{day:3,hour:9,categoryName:"Work/Coding",important:!0,urgent:!1,description:"Architecture design"},{day:3,hour:10,categoryName:"Work/Coding",important:!0,urgent:!1,description:"Implementation"},{day:3,hour:14,categoryName:"Learning/Study",important:!0,urgent:!1,description:"Research new tech"},{day:3,hour:18,categoryName:"Exercise/Health",important:!0,urgent:!1,description:"Yoga session"},{day:3,hour:20,categoryName:"Rest/Entertainment",important:!1,urgent:!1,description:"Read book"},{day:4,hour:9,categoryName:"Work/Coding",important:!0,urgent:!1,description:"Code cleanup"},{day:4,hour:10,categoryName:"Work/Coding",important:!1,urgent:!0,description:"Weekly report"},{day:4,hour:11,categoryName:"Work/Coding",important:!0,urgent:!1,description:"Team retrospective"},{day:4,hour:15,categoryName:"Learning/Study",important:!0,urgent:!1,description:"Skill practice"},{day:4,hour:19,categoryName:"Family/Social",important:!0,urgent:!1,description:"Friends meetup"},{day:5,hour:10,categoryName:"Exercise/Health",important:!0,urgent:!1,description:"Morning run"},{day:5,hour:14,categoryName:"Family/Social",important:!0,urgent:!1,description:"Family time"},{day:5,hour:16,categoryName:"Rest/Entertainment",important:!1,urgent:!1,description:"Hobby project"},{day:5,hour:20,categoryName:"Rest/Entertainment",important:!1,urgent:!1,description:"Movie night"},{day:6,hour:11,categoryName:"Exercise/Health",important:!0,urgent:!1,description:"Outdoor activity"},{day:6,hour:14,categoryName:"Learning/Study",important:!0,urgent:!1,description:"Personal project"},{day:6,hour:16,categoryName:"Family/Social",important:!0,urgent:!1,description:"Social gathering"},{day:6,hour:19,categoryName:"Rest/Entertainment",important:!1,urgent:!1,description:"Relax time"}].forEach(t=>{let s=e.find(e=>e.name===t.categoryName);if(s&&r[t.day]){let e={id:"sample-".concat(t.day,"-").concat(t.hour),date:(0,n.bU)(r[t.day]),hour:t.hour,categoryId:s.id,isImportant:t.important,isUrgent:t.urgent,description:t.description,createdAt:new Date,updatedAt:new Date};a.push(e)}}),a}(t,r))try{await a(e.date,e.hour,{categoryId:e.categoryId,isImportant:e.isImportant,isUrgent:e.isUrgent,description:e.description})}catch(e){console.warn("Failed to add sample entry:",e)}}var Y=r(7726),K=r(534),$=r(6191),J=r(5710);function X(){let{state:e,actions:t}=(0,o.A)(),{activeTab:r,setActiveTab:n}=E(),[l,c]=(0,s.useState)(!1),[d,m]=(0,s.useState)(!1),[x,g]=(0,s.useState)(!1),u=async()=>{m(!0);try{await V(e.timeEntries,e.categories,e.currentWeek,t.upsertTimeEntry)}catch(e){console.error("Failed to load sample data:",e)}finally{m(!1)}},h=async()=>{if(confirm("This will replace ALL existing categories with 8 predefined default categories. This action cannot be undone. Continue?")){g(!0);try{await t.loadDefaultCategories()}catch(e){console.error("Failed to load default categories:",e)}finally{g(!1)}}};return(0,a.jsx)("aside",{className:"w-full h-full bg-white overflow-y-auto custom-scrollbar",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex space-x-1 mb-6",children:[(0,a.jsxs)("button",{onClick:()=>n("categories"),className:"flex-1 flex items-center justify-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ".concat("categories"===r?"bg-primary-100 text-primary-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),children:[(0,a.jsx)(Y.A,{className:"h-4 w-4 mr-2"}),"Categories"]}),(0,a.jsxs)("button",{onClick:()=>n("stats"),className:"flex-1 flex items-center justify-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ".concat("stats"===r?"bg-primary-100 text-primary-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),children:[(0,a.jsx)(K.A,{className:"h-4 w-4 mr-2"}),"Stats"]})]}),"categories"===r&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(i,{onClick:()=>c(!0),className:"w-full",size:"sm",children:[(0,a.jsx)($.A,{className:"h-4 w-4 mr-2"}),"Add Category"]}),(0,a.jsxs)(i,{onClick:h,variant:"secondary",size:"sm",isLoading:x,className:"w-full",children:[(0,a.jsx)(J.A,{className:"h-4 w-4 mr-2"}),x?"Loading...":"Load Default Categories"]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 text-center",children:"Replaces all categories with 8 predefined ones"})]}),l&&(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-4 bg-gray-50",children:(0,a.jsx)(k,{onSuccess:()=>c(!1),onCancel:()=>c(!1)})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:["Categories (",e.categories.length,")"]}),(0,a.jsx)(S,{})]})]}),"stats"===r&&(0,a.jsxs)("div",{className:"space-y-6",children:[q(e.timeEntries)&&(0,a.jsxs)("div",{children:[(0,a.jsxs)(i,{onClick:u,variant:"secondary",size:"sm",isLoading:d,className:"w-full",children:[(0,a.jsx)(J.A,{className:"h-4 w-4 mr-2"}),"Load Sample Data"]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2 text-center",children:"Add sample time entries to see charts in action"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Quick Stats"}),(0,a.jsx)(B,{})]})]})]})})}function Z(e){var t;let{date:r,hour:l,onClose:c}=e,{state:d,actions:m}=(0,o.A)(),[x,g]=(0,s.useState)(!1),[u,h]=(0,s.useState)({categoryId:"",isImportant:!1,isUrgent:!1,description:""}),y=null==(t=d.plannedEntries)?void 0:t.find(e=>e.date===r&&e.hour===l);(0,s.useEffect)(()=>{y&&h({categoryId:y.categoryId,isImportant:y.isImportant,isUrgent:y.isUrgent,description:y.description||""})},[y]);let p=async e=>{if(e.preventDefault(),u.categoryId){g(!0);try{y?await m.updatePlannedEntry(y.id,u):await m.createPlannedEntry(r,l,u),c()}catch(e){console.error("Failed to save planned entry:",e)}finally{g(!1)}}},b=async()=>{if(y){g(!0);try{await m.deletePlannedEntry(y.id),c()}catch(e){console.error("Failed to delete planned entry:",e)}finally{g(!1)}}};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:[y?"Edit":"Plan"," Activity"]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[new Date(r).toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric"})," at ",(0,n.KY)(l)]})]}),(0,a.jsx)("button",{onClick:c,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(_.A,{className:"h-5 w-5"})})]}),(0,a.jsxs)("form",{onSubmit:p,className:"p-6 space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:"Category *"}),(0,a.jsxs)("select",{id:"category",value:u.categoryId,onChange:e=>h({...u,categoryId:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select a category"}),d.categories.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"Description (optional)"}),(0,a.jsx)("input",{type:"text",id:"description",value:u.description,onChange:e=>h({...u,description:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"What do you plan to do?"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"important",checked:u.isImportant,onChange:e=>h({...u,isImportant:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"important",className:"ml-2 block text-sm text-gray-900",children:"Important"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"urgent",checked:u.isUrgent,onChange:e=>h({...u,isUrgent:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"urgent",className:"ml-2 block text-sm text-gray-900",children:"Urgent"})]})]}),(0,a.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,a.jsxs)(i,{type:"submit",isLoading:x,className:"flex-1",disabled:!u.categoryId,children:[y?"Update":"Save"," Plan"]}),y&&(0,a.jsx)(i,{type:"button",variant:"danger",onClick:b,disabled:x,className:"px-3",children:"Delete"}),(0,a.jsx)(i,{type:"button",variant:"secondary",onClick:c,disabled:x,children:"Cancel"})]})]})]})})}function ee(e){let{value:t,onChange:r,categories:n}=e,[l,i]=(0,s.useState)(!1),[o,c]=(0,s.useState)(!1),d=n.find(e=>e.id===t);return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>i(!l),className:"w-full flex items-center justify-between px-3 py-2 border border-gray-300 rounded-lg bg-white hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors",children:[(0,a.jsx)("div",{className:"flex items-center",children:d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"category-color-dot",style:{backgroundColor:d.color}}),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:d.name})]}):(0,a.jsx)("span",{className:"text-sm text-gray-500",children:"Select a category"})}),(0,a.jsx)(y.A,{className:"h-4 w-4 text-gray-400 transition-transform ".concat(l?"rotate-180":"")})]}),l&&(0,a.jsxs)("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:[(0,a.jsx)("div",{className:"py-1",children:n.map(e=>(0,a.jsxs)("button",{type:"button",onClick:()=>{r(e.id),i(!1)},className:"w-full flex items-center px-3 py-2 text-left hover:bg-gray-50 transition-colors ".concat(t===e.id?"bg-primary-50 text-primary-700":"text-gray-900"),children:[(0,a.jsx)("div",{className:"category-color-dot",style:{backgroundColor:e.color}}),(0,a.jsx)("span",{className:"text-sm",children:e.name})]},e.id))}),(0,a.jsx)("div",{className:"border-t border-gray-200 py-1",children:(0,a.jsxs)("button",{type:"button",onClick:()=>{c(!0),i(!1)},className:"w-full flex items-center px-3 py-2 text-left text-primary-600 hover:bg-primary-50 transition-colors",children:[(0,a.jsx)($.A,{className:"h-4 w-4 mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Add New Category"})]})})]}),l&&(0,a.jsx)("div",{className:"fixed inset-0 z-5",onClick:()=>i(!1)}),o&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-xl max-w-sm w-full p-6",children:(0,a.jsx)(k,{onSuccess:()=>{c(!1)},onCancel:()=>c(!1)})})})]})}function et(e){let{date:t,hour:r,onClose:l}=e,{state:c,actions:d}=(0,o.A)(),[m,x]=(0,s.useState)({categoryId:"",isImportant:!1,isUrgent:!1,description:""}),[g,u]=(0,s.useState)(!1),[h,y]=(0,s.useState)(null),p=c.timeEntries.find(e=>e.date===t&&e.hour===r);(0,s.useEffect)(()=>{p?x({categoryId:p.categoryId,isImportant:p.isImportant,isUrgent:p.isUrgent,description:p.description||""}):c.categories.length>0&&x(e=>({...e,categoryId:c.categories[0].id}))},[p,c.categories]);let b=async e=>{if(e.preventDefault(),!m.categoryId)return void y("Please select a category");u(!0),y(null);try{await d.upsertTimeEntry(t,r,m),l()}catch(e){y(e instanceof Error?e.message:"Failed to save time entry")}finally{u(!1)}},f=async()=>{if(p){u(!0);try{await d.deleteTimeEntry(t,r),l()}catch(e){y(e instanceof Error?e.message:"Failed to delete time entry")}finally{u(!1)}}};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:[p?"Edit":"Add"," Time Entry"]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[new Date(t).toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric"})," at ",(0,n.KY)(r)]})]}),(0,a.jsx)("button",{onClick:l,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(_.A,{className:"h-5 w-5"})})]}),(0,a.jsxs)("form",{onSubmit:b,className:"p-6 space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Category *"}),(0,a.jsx)(ee,{value:m.categoryId,onChange:e=>x({...m,categoryId:e}),categories:c.categories})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Eisenhower Matrix"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:m.isImportant,onChange:e=>x({...m,isImportant:e.target.checked}),className:"checkbox-field"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Important"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:m.isUrgent,onChange:e=>x({...m,isUrgent:e.target.checked}),className:"checkbox-field"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Urgent"})]})]}),(0,a.jsxs)("div",{className:"mt-2 text-xs text-gray-500 flex items-center",children:[(0,a.jsx)("span",{className:"mr-1",children:m.isImportant&&m.isUrgent?"\uD83D\uDD25":m.isImportant&&!m.isUrgent?"⭐":!m.isImportant&&m.isUrgent?"⚡":"\uD83D\uDCA4"}),"Quadrant: ",m.isImportant&&m.isUrgent?"Q1 (Do First)":m.isImportant&&!m.isUrgent?"Q2 (Schedule)":!m.isImportant&&m.isUrgent?"Q3 (Delegate)":"Q4 (Eliminate)"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"Description (optional)"}),(0,a.jsx)("textarea",{id:"description",value:m.description,onChange:e=>x({...m,description:e.target.value}),placeholder:"Brief description of the activity...",className:"input-field resize-none",rows:3,maxLength:200}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[m.description.length,"/200 characters"]})]}),h&&(0,a.jsx)("div",{className:"text-sm text-red-600 bg-red-50 p-3 rounded",children:h}),(0,a.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,a.jsxs)(i,{type:"submit",isLoading:g,className:"flex-1",children:[p?"Update":"Save"," Entry"]}),p&&(0,a.jsx)(i,{type:"button",variant:"danger",onClick:f,disabled:g,className:"px-3",children:(0,a.jsx)(N.A,{className:"h-4 w-4"})}),(0,a.jsx)(i,{type:"button",variant:"secondary",onClick:l,disabled:g,children:"Cancel"})]})]})]})})}function er(e){let t=parseInt(e.slice(1,3),16),r=parseInt(e.slice(3,5),16);return(.299*t+.587*r+.114*parseInt(e.slice(5,7),16))/255>.5?"#000000":"#FFFFFF"}function ea(e){let{timeSlot:t,onClick:r,onPlanClick:s,onClearCell:l,isToday:i=!1,showPlanSection:o=!0}=e,{actualEntry:c,actualCategory:d,plannedEntry:m,plannedCategory:x}=t,g=!!c,u=!!m,h=e=>e?e.isImportant&&e.isUrgent?{symbol:"\uD83D\uDD25",bgColor:"#ef4444",label:"Q1: Do First"}:e.isImportant&&!e.isUrgent?{symbol:"⭐",bgColor:"#10b981",label:"Q2: Schedule"}:!e.isImportant&&e.isUrgent?{symbol:"⚡",bgColor:"#f59e0b",label:"Q3: Delegate"}:{symbol:"\uD83D\uDCA4",bgColor:"#6b7280",label:"Q4: Eliminate"}:null,y=h(c),p=h(m),b=u&&g?m.categoryId===c.categoryId?"completed":"different":u&&!g?"missed":!u&&g?"unplanned":"empty";return o?(0,a.jsxs)("div",{className:(0,n.cn)("time-slot border-r border-gray-200 last:border-r-0 relative transition-all duration-200",i?"border-primary-200":"","completed"===b&&"ring-2 ring-green-300","different"===b&&"ring-2 ring-yellow-300","missed"===b&&"ring-2 ring-red-300","unplanned"===b&&"ring-2 ring-blue-300"),children:[(0,a.jsxs)("div",{className:"flex h-full min-h-[60px] overflow-hidden",children:[(0,a.jsx)("div",{className:(0,n.cn)("w-1/2 border-r border-gray-300 relative cursor-pointer transition-all duration-200 hover:bg-gray-50 overflow-hidden",u&&x?"bg-opacity-30":""),style:{backgroundColor:u&&x?"".concat(x.color,"20"):void 0},onClick:s,children:(0,a.jsxs)("div",{className:"p-1 h-full flex flex-col justify-between overflow-hidden",children:[u&&x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"text-xs font-medium truncate text-gray-700 overflow-hidden",children:x.name}),m.description&&(0,a.jsx)("div",{className:"text-xs text-gray-600 truncate mt-1 overflow-hidden",children:m.description})]}):(0,a.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"Plan"})}),p&&(0,a.jsx)("div",{className:"absolute top-0.5 right-0.5 flex items-center justify-center w-4 h-4 rounded-full text-white text-xs font-bold shadow-sm opacity-70",style:{backgroundColor:p.bgColor},title:"Planned: ".concat(p.label),children:(0,a.jsx)("span",{className:"text-xs leading-none",children:p.symbol})})]})}),(0,a.jsx)("div",{className:(0,n.cn)("w-1/2 relative cursor-pointer transition-all duration-200 overflow-hidden",g?"filled":"hover:bg-gray-50"),style:{backgroundColor:g&&d?d.color:void 0,color:g&&d?er(d.color):void 0},onClick:r,children:(0,a.jsxs)("div",{className:"p-1 h-full flex flex-col justify-between overflow-hidden",children:[g&&d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"text-xs font-medium truncate overflow-hidden",children:d.name}),c.description&&(0,a.jsx)("div",{className:"text-xs opacity-80 truncate mt-1 overflow-hidden",children:c.description})]}):(0,a.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"Track"})}),y&&(0,a.jsx)("div",{className:"absolute top-0.5 right-0.5 flex items-center justify-center w-4 h-4 rounded-full text-white text-xs font-bold shadow-sm",style:{backgroundColor:y.bgColor},title:"Actual: ".concat(y.label),children:(0,a.jsx)("span",{className:"text-xs leading-none",children:y.symbol})})]})})]}),"empty"!==b&&(0,a.jsxs)("div",{className:"absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white",children:["completed"===b&&(0,a.jsx)("div",{className:"w-full h-full bg-green-500 rounded-full",title:"Plan completed as expected"}),"different"===b&&(0,a.jsx)("div",{className:"w-full h-full bg-yellow-500 rounded-full",title:"Different activity than planned"}),"missed"===b&&(0,a.jsx)("div",{className:"w-full h-full bg-red-500 rounded-full",title:"Planned activity missed"}),"unplanned"===b&&(0,a.jsx)("div",{className:"w-full h-full bg-blue-500 rounded-full",title:"Unplanned activity"})]}),(u||g)&&l&&(0,a.jsx)("button",{onClick:e=>{e.stopPropagation(),l()},className:"absolute top-1 left-1 opacity-0 hover:opacity-100 transition-opacity bg-red-500 hover:bg-red-600 text-white rounded-full p-1 shadow-sm z-10",title:"Clear this time slot",children:(0,a.jsx)(_.A,{className:"h-3 w-3"})})]}):(0,a.jsx)("div",{className:(0,n.cn)("time-slot border-r border-gray-200 last:border-r-0 relative cursor-pointer transition-all duration-200",g?"filled":"",i?"border-primary-200":""),style:{backgroundColor:g&&d?d.color:void 0,color:g&&d?er(d.color):void 0},onClick:r,children:(0,a.jsxs)("div",{className:"p-2 h-full min-h-[60px] flex flex-col justify-between",children:[g&&d&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"text-xs font-medium truncate",children:d.name}),c.description&&(0,a.jsx)("div",{className:"text-xs opacity-80 truncate mt-1",children:c.description})]}),y&&(0,a.jsx)("div",{className:"absolute top-1 right-1 flex items-center justify-center w-6 h-6 rounded-full text-white text-xs font-bold shadow-sm",style:{backgroundColor:y.bgColor},title:y.label,children:(0,a.jsx)("span",{className:"text-sm leading-none",children:y.symbol})}),!g&&(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-gray-100 bg-opacity-50",children:(0,a.jsx)("span",{className:"text-xs text-gray-600 font-medium",children:"+ Add"})}),g&&l&&(0,a.jsx)("button",{onClick:e=>{e.stopPropagation(),l()},className:"absolute top-1 left-1 opacity-0 hover:opacity-100 transition-opacity bg-red-500 hover:bg-red-600 text-white rounded-full p-1 shadow-sm",title:"Clear this time slot",children:(0,a.jsx)(_.A,{className:"h-3 w-3"})})]})})}function es(){let{state:e,actions:t}=(0,o.A)(),[r,l]=(0,s.useState)(null),[i,c]=(0,s.useState)(null),d=(0,n._2)(e.currentWeek),m=(0,n.F_)(d,e.timeEntries,e.categories,e.plannedEntries),x=Array.from({length:w.Oc.TOTAL},(e,t)=>w.Oc.START+t),g=async(e,r)=>{if(confirm("Are you sure you want to clear this time slot? This action cannot be undone."))try{await t.clearCellData(e,r)}catch(e){console.error("Failed to clear cell data:",e)}};return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:[(0,a.jsxs)("div",{className:"bg-gray-50 border-b border-gray-200 p-4",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Weekly Time Grid"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Click any time slot to add or edit an activity"})]}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("div",{className:"min-w-[800px]",children:[(0,a.jsxs)("div",{className:"grid grid-cols-8 border-b border-gray-200",children:[(0,a.jsx)("div",{className:"p-3 bg-gray-50 border-r border-gray-200",children:(0,a.jsx)("span",{className:"text-xs font-medium text-gray-500",children:"Time"})}),m.map(e=>(0,a.jsxs)("div",{className:"p-3 text-center border-r border-gray-200 last:border-r-0 ".concat((0,n.cK)(e.dateObj)?"bg-primary-50":"bg-gray-50"),children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.dayName}),(0,a.jsx)("div",{className:"text-xs mt-1 ".concat((0,n.cK)(e.dateObj)?"text-primary-600":"text-gray-500"),children:e.dateObj.getDate()})]},e.date))]}),x.map(e=>(0,a.jsxs)("div",{className:"grid grid-cols-8 border-b border-gray-200 last:border-b-0",children:[(0,a.jsx)("div",{className:"p-3 bg-gray-50 border-r border-gray-200 flex items-center",children:(0,a.jsx)("span",{className:"text-xs font-medium text-gray-600",children:(0,n.KY)(e)})}),m.map(t=>{let r=t.timeSlots.find(t=>t.hour===e);return(0,a.jsx)(ea,{timeSlot:r,onClick:()=>{l({date:t.date,hour:e})},onPlanClick:()=>{c({date:t.date,hour:e})},onClearCell:()=>g(t.date,e),isToday:(0,n.cK)(t.dateObj),showPlanSection:!0},"".concat(t.date,"-").concat(e))})]},e))]})}),r&&(0,a.jsx)(et,{date:r.date,hour:r.hour,onClose:()=>{l(null)}}),i&&(0,a.jsx)(Z,{date:i.date,hour:i.hour,onClose:()=>{c(null)}})]})}var en=r(6132);function el(e){let{message:t,onRetry:r}=e;return(0,a.jsx)("div",{className:"card max-w-md mx-auto text-center",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,a.jsx)(en.A,{className:"h-12 w-12 text-red-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Something went wrong"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:t})]}),r&&(0,a.jsx)("button",{onClick:r,className:"btn-primary",children:"Try Again"})]})})}function ei(){let{activeTab:e}=E(),{state:t}=(0,o.A)();return t.isLoading?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(l,{})}):t.error?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(el,{message:t.error})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(v,{}),(0,a.jsx)("div",{className:"flex h-[calc(100vh-73px)]",children:"stats"===e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(G,{defaultWidth:450,minWidth:350,maxWidth:800,storageKey:"stats-panel-width",children:(0,a.jsx)("div",{className:"h-full bg-white",children:(0,a.jsx)(X,{})})}),(0,a.jsx)("main",{className:"flex-1 p-6 overflow-auto",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,a.jsx)(es,{})})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(G,{defaultWidth:320,minWidth:250,maxWidth:500,storageKey:"sidebar-width",children:(0,a.jsx)(X,{})}),(0,a.jsx)("main",{className:"flex-1 p-6 overflow-auto",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,a.jsx)(es,{})})})]})})]})}function eo(){return(0,a.jsx)(A,{children:(0,a.jsx)(ei,{})})}}},e=>{e.O(0,[647,430,108,86,441,255,358],()=>e(e.s=2855)),_N_E=e.O()}]);